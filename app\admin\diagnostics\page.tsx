"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Loader2, RefreshCw, Save, ShieldCheck, ShieldAlert, Download } from "lucide-react"
import { PWADiagnostics } from "@/components/pwa-diagnostics"
import { PWATestSuite } from "@/components/pwa-test-suite"
import { PWAPerformanceMonitor } from "@/components/pwa-performance-monitor"
import { PWAAuditReportComponent } from "@/components/pwa-audit-report"
import { PWAAuditSummary } from "@/components/pwa-audit-summary"
import { PWAIconDiagnostics } from "@/components/pwa-icon-diagnostics"
import { FavoritesTest } from "@/components/favorites-test"

export default function DiagnosticsPage() {
  const { user, userData, refreshToken, restoreSession } = useAuth()
  const [diagnostics, setDiagnostics] = useState({
    localStorage: {} as Record<string, string>,
    indexedDBAvailable: false,
    serviceWorkerActive: false,
    secureContext: false,
    installStatus: "unknown",
    browserInfo: "",
  })

  const [message, setMessage] = useState<{ type: "success" | "error" | "info"; text: string } | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Fonction pour rafraîchir les diagnostics
  const refreshDiagnostics = async () => {
    setIsRefreshing(true)
    try {
      // Récupérer les clés d'authentification de localStorage
      const localStorageKeys = [
        "auth_user_uid",
        "auth_last_active",
        "auth_last_token",
        "firebase_persistence_method",
        "auth_user_v2",
        "auth_session_v2",
        "auth_timestamp_v2",
        "auth_persistence_enabled",
        "auth_needs_restore",
        "indexed_db_available",
        "service_worker_supported",
        "is_secure_context",
        "auth_persistence_helper_loaded",
        "auth_persistence_helper_loaded_at",
      ]

      const localStorageData: Record<string, string> = {}
      localStorageKeys.forEach((key) => {
        const value = localStorage.getItem(key)
        if (value) {
          if (key === "auth_last_token" || key === "auth_session_v2") {
            // Masquer le token pour des raisons de sécurité
            localStorageData[key] =
              value.length > 20 ? `${value.substring(0, 10)}...${value.substring(value.length - 10)}` : value
          } else {
            localStorageData[key] = value
          }
        } else {
          localStorageData[key] = "non défini"
        }
      })

      // Vérifier si IndexedDB est disponible
      let indexedDBAvailable = false
      try {
        indexedDBAvailable = "indexedDB" in window && window.indexedDB !== null
      } catch (e) {
        console.error("Erreur lors de la vérification d'IndexedDB:", e)
      }

      // Vérifier si le Service Worker est actif
      let serviceWorkerActive = false
      try {
        serviceWorkerActive = "serviceWorker" in navigator && !!navigator.serviceWorker.controller
      } catch (e) {
        console.error("Erreur lors de la vérification du Service Worker:", e)
      }

      // Vérifier si nous sommes dans un contexte sécurisé
      let secureContext = false
      try {
        secureContext =
          window.isSecureContext || window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1"
      } catch (e) {
        console.error("Erreur lors de la vérification du contexte sécurisé:", e)
      }

      // Déterminer le statut d'installation
      let installStatus = "non installée"
      try {
        if (
          window.matchMedia("(display-mode: standalone)").matches ||
          window.matchMedia("(display-mode: fullscreen)").matches
        ) {
          installStatus = "installée (standalone)"
        } else if (window.matchMedia("(display-mode: minimal-ui)").matches) {
          installStatus = "installée (minimal-ui)"
        } else if (window.navigator.standalone === true) {
          installStatus = "installée (iOS standalone)"
        }
      } catch (e) {
        console.error("Erreur lors de la vérification du statut d'installation:", e)
      }

      // Récupérer les informations sur le navigateur
      const browserInfo = `${navigator.userAgent}`

      setDiagnostics({
        localStorage: localStorageData,
        indexedDBAvailable,
        serviceWorkerActive,
        secureContext,
        installStatus,
        browserInfo,
      })

      setLastUpdated(new Date())
      setMessage({ type: "success", text: "Diagnostics mis à jour avec succès" })
    } catch (error) {
      console.error("Erreur lors du rafraîchissement des diagnostics:", error)
      setMessage({ type: "error", text: "Erreur lors de la mise à jour des diagnostics" })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Rafraîchir les diagnostics au chargement
  useEffect(() => {
    refreshDiagnostics()
  }, [])

  // Effacer le message après 5 secondes
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  // Fonction pour forcer le rafraîchissement du token
  const handleRefreshToken = async () => {
    try {
      setIsRefreshing(true)
      await refreshToken()
      setMessage({ type: "success", text: "Token rafraîchi avec succès" })
      refreshDiagnostics()
    } catch (error) {
      console.error("Erreur lors du rafraîchissement du token:", error)
      setMessage({ type: "error", text: "Erreur lors du rafraîchissement du token" })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Fonction pour restaurer la session
  const handleRestoreSession = async () => {
    try {
      setIsRefreshing(true)
      const result = await restoreSession()
      if (result) {
        setMessage({ type: "success", text: "Session restaurée avec succès" })
      } else {
        setMessage({ type: "error", text: "Impossible de restaurer la session" })
      }
      refreshDiagnostics()
    } catch (error) {
      console.error("Erreur lors de la restauration de la session:", error)
      setMessage({ type: "error", text: "Erreur lors de la restauration de la session" })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Fonction pour exporter les logs de diagnostic
  const exportDiagnostics = () => {
    try {
      // Créer un objet contenant toutes les informations de diagnostic
      const exportData = {
        timestamp: new Date().toISOString(),
        user: user ? { uid: user.uid, email: user.email } : null,
        userData: userData,
        diagnostics,
        browserInfo: navigator.userAgent,
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      }

      // Convertir en JSON
      const jsonData = JSON.stringify(exportData, null, 2)

      // Créer un blob et un lien de téléchargement
      const blob = new Blob([jsonData], { type: "application/json" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `acr-direct-diagnostics-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`
      document.body.appendChild(a)
      a.click()

      // Nettoyer
      setTimeout(() => {
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }, 100)

      setMessage({ type: "success", text: "Logs de diagnostic exportés avec succès" })
    } catch (error) {
      console.error("Erreur lors de l'exportation des logs:", error)
      setMessage({ type: "error", text: "Erreur lors de l'exportation des logs" })
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Diagnostics système</h1>
        <p className="text-muted-foreground">
          Analysez et résolvez les problèmes de persistance d'authentification dans l'application PWA
        </p>
      </div>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Diagnostic de Persistance de Session</CardTitle>
          <CardDescription>
            Analysez et résolvez les problèmes de persistance d'authentification dans l'application PWA
          </CardDescription>
        </CardHeader>
        <CardContent>
          {message && (
            <Alert
              className={`mb-4 ${message.type === "success" ? "bg-green-50" : message.type === "error" ? "bg-red-50" : "bg-blue-50"}`}
            >
              <AlertTitle>
                {message.type === "success" ? "Succès" : message.type === "error" ? "Erreur" : "Information"}
              </AlertTitle>
              <AlertDescription>{message.text}</AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="status">
            <TabsList className="mb-4 grid grid-cols-2 md:grid-cols-5 gap-2">
              <TabsTrigger value="status">État Actuel</TabsTrigger>
              <TabsTrigger value="storage">Stockage</TabsTrigger>
              <TabsTrigger value="environment">Environnement</TabsTrigger>
              <TabsTrigger value="pwa">PWA</TabsTrigger>
              <TabsTrigger value="actions">Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="status">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">État de l'authentification</h3>
                  <Button variant="outline" size="sm" onClick={refreshDiagnostics} disabled={isRefreshing}>
                    {isRefreshing ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                    <span className="ml-2">Actualiser</span>
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2 border rounded-md p-4">
                    <h4 className="font-medium">État de la session</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <span>Utilisateur connecté:</span>
                      <span className={user ? "text-green-600 font-medium" : "text-red-600 font-medium"}>
                        {user ? "Oui ✓" : "Non ✗"}
                      </span>

                      <span>Email:</span>
                      <span className="font-medium">{user?.email || "Non connecté"}</span>

                      <span>UID:</span>
                      <span className="font-medium">{user?.uid || "Non connecté"}</span>

                      <span>Rôle:</span>
                      <span className="font-medium">{userData?.role || "Non défini"}</span>

                      <span>Méthode de persistance:</span>
                      <span className="font-medium">
                        {diagnostics.localStorage["firebase_persistence_method"] || "Non définie"}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2 border rounded-md p-4">
                    <h4 className="font-medium">Horodatages</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <span>Dernier rafraîchissement:</span>
                      <span className="font-medium">
                        {diagnostics.localStorage["auth_timestamp_v2"]
                          ? new Date(Number.parseInt(diagnostics.localStorage["auth_timestamp_v2"])).toLocaleString()
                          : "Jamais"}
                      </span>

                      <span>Dernière activité:</span>
                      <span className="font-medium">
                        {diagnostics.localStorage["auth_last_active"]
                          ? new Date(Number.parseInt(diagnostics.localStorage["auth_last_active"])).toLocaleString()
                          : "Jamais"}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2 border rounded-md p-4">
                  <h4 className="font-medium">Statut de l'application</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                    <span>Application PWA:</span>
                    <span className="font-medium">{diagnostics.installStatus}</span>

                    <span>Service Worker:</span>
                    <span
                      className={
                        diagnostics.serviceWorkerActive ? "text-green-600 font-medium" : "text-red-600 font-medium"
                      }
                    >
                      {diagnostics.serviceWorkerActive ? "Actif ✓" : "Inactif ✗"}
                    </span>

                    <span>Contexte sécurisé:</span>
                    <span
                      className={diagnostics.secureContext ? "text-green-600 font-medium" : "text-red-600 font-medium"}
                    >
                      {diagnostics.secureContext ? "Oui ✓" : "Non ✗"}
                    </span>

                    <span>IndexedDB disponible:</span>
                    <span
                      className={
                        diagnostics.indexedDBAvailable ? "text-green-600 font-medium" : "text-red-600 font-medium"
                      }
                    >
                      {diagnostics.indexedDBAvailable ? "Oui ✓" : "Non ✗"}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Navigateur</h4>
                  <div className="text-sm border rounded-md p-2 bg-gray-50 dark:bg-gray-900 overflow-auto">
                    {diagnostics.browserInfo}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="storage">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Données de stockage</h3>
                  <Button variant="outline" size="sm" onClick={refreshDiagnostics} disabled={isRefreshing}>
                    {isRefreshing ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                    <span className="ml-2">Actualiser</span>
                  </Button>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">LocalStorage</h4>
                  <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-md text-sm overflow-auto max-h-80">
                    <pre className="whitespace-pre-wrap">{JSON.stringify(diagnostics.localStorage, null, 2)}</pre>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Mécanismes de stockage disponibles</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-md p-3">
                      <div className="flex items-center mb-2">
                        <span className={`mr-2 ${diagnostics.indexedDBAvailable ? "text-green-500" : "text-red-500"}`}>
                          {diagnostics.indexedDBAvailable ? <ShieldCheck size={20} /> : <ShieldAlert size={20} />}
                        </span>
                        <h5 className="font-medium">IndexedDB</h5>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {diagnostics.indexedDBAvailable
                          ? "IndexedDB est disponible et peut être utilisé pour stocker les données d'authentification de manière persistante."
                          : "IndexedDB n'est pas disponible, ce qui peut affecter la persistance de l'authentification."}
                      </p>
                    </div>

                    <div className="border rounded-md p-3">
                      <div className="flex items-center mb-2">
                        <span className={`mr-2 ${diagnostics.serviceWorkerActive ? "text-green-500" : "text-red-500"}`}>
                          {diagnostics.serviceWorkerActive ? <ShieldCheck size={20} /> : <ShieldAlert size={20} />}
                        </span>
                        <h5 className="font-medium">Service Worker</h5>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {diagnostics.serviceWorkerActive
                          ? "Le Service Worker est actif et peut aider à maintenir la session d'authentification."
                          : "Le Service Worker n'est pas actif, ce qui peut affecter la persistance de l'authentification."}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="environment">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Environnement d'exécution</h3>
                  <p className="text-sm text-muted-foreground">
                    Ces facteurs peuvent affecter la persistance de l'authentification dans votre application PWA.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Contexte de sécurité</h4>
                    <div className="flex items-center mb-2">
                      <span className={`mr-2 ${diagnostics.secureContext ? "text-green-500" : "text-red-500"}`}>
                        {diagnostics.secureContext ? <ShieldCheck size={20} /> : <ShieldAlert size={20} />}
                      </span>
                      <span className="text-sm font-medium">
                        {diagnostics.secureContext ? "Contexte sécurisé" : "Contexte non sécurisé"}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {diagnostics.secureContext
                        ? "L'application s'exécute dans un contexte sécurisé (HTTPS ou localhost), ce qui permet l'utilisation complète des API de stockage."
                        : "L'application ne s'exécute pas dans un contexte sécurisé, ce qui limite l'accès à certaines API de stockage."}
                    </p>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Mode d'installation</h4>
                    <p className="text-sm mb-2">
                      <span className="font-medium">Statut actuel: </span>
                      {diagnostics.installStatus}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Le mode d'installation peut affecter la façon dont le navigateur gère le stockage et la
                      persistance des données. Les applications installées en mode "standalone" ont généralement un
                      meilleur accès au stockage persistant.
                    </p>
                  </div>
                </div>

                <div className="border rounded-md p-4">
                  <h4 className="font-medium mb-2">Limitations spécifiques au navigateur</h4>
                  <div className="space-y-2 text-sm">
                    <p>
                      <span className="font-medium">iOS Safari: </span>
                      Limite le stockage à 7 jours et peut effacer les données après cette période ou si l'appareil
                      manque d'espace.
                    </p>
                    <p>
                      <span className="font-medium">Android Chrome: </span>
                      Généralement plus permissif, mais peut toujours effacer les données si l'appareil manque d'espace.
                    </p>
                    <p>
                      <span className="font-medium">Mode privé/incognito: </span>
                      Les données de stockage sont généralement effacées à la fermeture de l'application.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="pwa">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Diagnostic PWA</h3>
                  <p className="text-sm text-muted-foreground">
                    État de l'installation et des capacités PWA
                  </p>
                </div>
                <PWAAuditSummary />
                <PWAIconDiagnostics />
                <PWADiagnostics />
                <PWATestSuite />
                <PWAPerformanceMonitor />
                <PWAAuditReportComponent />
              </div>
            </TabsContent>

            <TabsContent value="actions">
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Actions de diagnostic</h3>
                  <p className="text-sm text-muted-foreground">
                    Utilisez ces actions pour tester et améliorer la persistance de l'authentification.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4 space-y-4">
                    <h4 className="font-medium">Rafraîchissement de session</h4>
                    <p className="text-sm text-muted-foreground">
                      Rafraîchit le token d'authentification et met à jour les mécanismes de persistance.
                    </p>
                    <Button onClick={handleRefreshToken} disabled={!user || isRefreshing} className="w-full">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Rafraîchir le token
                    </Button>
                  </div>

                  <div className="border rounded-md p-4 space-y-4">
                    <h4 className="font-medium">Restauration de session</h4>
                    <p className="text-sm text-muted-foreground">
                      Tente de restaurer une session précédemment stockée.
                    </p>
                    <Button onClick={handleRestoreSession} disabled={!!user || isRefreshing} className="w-full">
                      <Save className="mr-2 h-4 w-4" />
                      Restaurer la session
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md p-4 space-y-4">
                  <h4 className="font-medium">Test de réactivité des favoris</h4>
                  <p className="text-sm text-muted-foreground">
                    Testez la mise à jour en temps réel du menu "Mes Favoris" lors de l'ajout/suppression de favoris.
                  </p>
                  <FavoritesTest />
                </div>

                <div className="border rounded-md p-4 space-y-4">
                  <h4 className="font-medium">Conseils pour améliorer la persistance</h4>
                  <div className="space-y-2 text-sm">
                    <p>
                      <span className="font-medium">1. Utilisation régulière: </span>
                      Ouvrez l'application au moins une fois tous les 7 jours pour maintenir le stockage sur iOS.
                    </p>
                    <p>
                      <span className="font-medium">2. Installation correcte: </span>
                      Assurez-vous que l'application est installée en mode "standalone" pour une meilleure persistance.
                    </p>
                    <p>
                      <span className="font-medium">3. Rafraîchissement périodique: </span>
                      Utilisez le bouton "Rafraîchir le token" régulièrement pour maintenir votre session active.
                    </p>
                    <p>
                      <span className="font-medium">4. Évitez le mode privé: </span>
                      N'utilisez pas l'application en mode de navigation privée/incognito.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between items-center border-t pt-4">
          <p className="text-xs text-muted-foreground">Dernière mise à jour: {lastUpdated.toLocaleString()}</p>
          <Button variant="outline" size="sm" onClick={exportDiagnostics}>
            <Download className="h-4 w-4 mr-2" />
            Exporter les logs
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
