"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, TestTube } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { useFavoritesStatus } from "@/lib/hooks/use-favorites-status"
import { addToFavorites, removeFromFavorites, getUserFavorites } from "@/lib/favorites-utils"
import { useToast } from "@/components/ui/use-toast"

/**
 * Test component to verify real-time favorites functionality
 * This component should only be visible to administrators for testing purposes
 */
export function FavoritesTest() {
  const { user, isAdmin } = useAuth()
  const { hasFavorites, isLoading } = useFavoritesStatus()
  const { toast } = useToast()
  const [testLoading, setTestLoading] = useState(false)
  const [favoritesCount, setFavoritesCount] = useState<number | null>(null)

  // Only show to admins
  if (!isAdmin) {
    return null
  }

  const testFavoriteId = "test-favorite-item"

  const handleAddTestFavorite = async () => {
    if (!user) return
    
    setTestLoading(true)
    try {
      const success = await addToFavorites(user.uid, testFavoriteId)
      if (success) {
        toast({
          title: "Test Favorite Added",
          description: "Test favorite added successfully. Menu should update immediately.",
          duration: 3000,
        })
        // Update count
        const favorites = await getUserFavorites(user.uid)
        setFavoritesCount(favorites.length)
      }
    } catch (error) {
      console.error("Error adding test favorite:", error)
      toast({
        title: "Error",
        description: "Failed to add test favorite",
        variant: "destructive",
      })
    } finally {
      setTestLoading(false)
    }
  }

  const handleRemoveTestFavorite = async () => {
    if (!user) return
    
    setTestLoading(true)
    try {
      const success = await removeFromFavorites(user.uid, testFavoriteId)
      if (success) {
        toast({
          title: "Test Favorite Removed",
          description: "Test favorite removed successfully. Menu should update immediately.",
          duration: 3000,
        })
        // Update count
        const favorites = await getUserFavorites(user.uid)
        setFavoritesCount(favorites.length)
      }
    } catch (error) {
      console.error("Error removing test favorite:", error)
      toast({
        title: "Error",
        description: "Failed to remove test favorite",
        variant: "destructive",
      })
    } finally {
      setTestLoading(false)
    }
  }

  const handleRefreshCount = async () => {
    if (!user) return
    
    try {
      const favorites = await getUserFavorites(user.uid)
      setFavoritesCount(favorites.length)
    } catch (error) {
      console.error("Error refreshing count:", error)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Favorites Real-time Test
        </CardTitle>
        <CardDescription>
          Test the real-time favorites functionality. Watch the menu update immediately.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Current Status:</span>
          <Badge variant={hasFavorites ? "default" : "secondary"}>
            {isLoading ? "Loading..." : hasFavorites ? "Has Favorites" : "No Favorites"}
          </Badge>
        </div>
        
        {favoritesCount !== null && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Favorites Count:</span>
            <Badge variant="outline">{favoritesCount}</Badge>
          </div>
        )}

        <div className="flex flex-col gap-2">
          <Button
            onClick={handleAddTestFavorite}
            disabled={testLoading || !user}
            size="sm"
            className="w-full"
          >
            <Star className="h-4 w-4 mr-2" />
            Add Test Favorite
          </Button>
          
          <Button
            onClick={handleRemoveTestFavorite}
            disabled={testLoading || !user}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Remove Test Favorite
          </Button>
          
          <Button
            onClick={handleRefreshCount}
            disabled={!user}
            variant="ghost"
            size="sm"
            className="w-full"
          >
            Refresh Count
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <p><strong>Expected behavior:</strong></p>
          <ul className="list-disc list-inside space-y-1 mt-1">
            <li>Menu "Mes Favoris" appears/disappears immediately</li>
            <li>Status badge updates in real-time</li>
            <li>No page reload required</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
