"use client"

import { useState, useEffect } from "react"
import { getUserFavorites } from "@/lib/favorites-utils"
import { useAuth } from "@/components/auth-provider"

// Cache key for favorites status
const FAVORITES_STATUS_CACHE_KEY = "user_has_favorites_"

// Event name for favorites status changes
const FAVORITES_STATUS_CHANGE_EVENT = "favoritesStatusChanged"

// Global event emitter for favorites status changes
class FavoritesEventEmitter {
  private listeners: Set<(hasFavorites: boolean) => void> = new Set()

  subscribe(callback: (hasFavorites: boolean) => void) {
    this.listeners.add(callback)
    return () => {
      this.listeners.delete(callback)
    }
  }

  emit(hasFavorites: boolean) {
    this.listeners.forEach(callback => callback(hasFavorites))
  }
}

const favoritesEventEmitter = new FavoritesEventEmitter()

// Export the event emitter for direct use
export { favoritesEventEmitter }

// Function to trigger favorites status update globally
export function triggerFavoritesStatusUpdate(userId: string, hasFavorites: boolean) {
  // Update cache
  const cacheKey = `${FAVORITES_STATUS_CACHE_KEY}${userId}`
  localStorage.setItem(cacheKey, hasFavorites.toString())
  localStorage.setItem(`${cacheKey}_timestamp`, Date.now().toString())

  // Emit event to all listeners
  favoritesEventEmitter.emit(hasFavorites)
}

export function useFavoritesStatus() {
  const { user } = useAuth()
  const [hasFavorites, setHasFavorites] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let isMounted = true

    const checkFavorites = async () => {
      if (!user) {
        if (isMounted) {
          setHasFavorites(false)
          setIsLoading(false)
        }
        return
      }

      try {
        // Check cache first
        const cacheKey = `${FAVORITES_STATUS_CACHE_KEY}${user.uid}`
        const cachedStatus = localStorage.getItem(cacheKey)
        const cachedTimestamp = localStorage.getItem(`${cacheKey}_timestamp`)

        // Use cache if it's recent (less than 5 minutes old)
        if (cachedStatus && cachedTimestamp) {
          const timestamp = Number.parseInt(cachedTimestamp)
          const now = Date.now()

          if (now - timestamp < 5 * 60 * 1000) {
            // 5 minutes
            if (isMounted) {
              setHasFavorites(cachedStatus === "true")
              setIsLoading(false)
            }
            return
          }
        }

        // If no valid cache, fetch from Firestore
        const favorites = await getUserFavorites(user.uid)
        const hasAny = favorites.length > 0

        // Update cache
        localStorage.setItem(cacheKey, hasAny.toString())
        localStorage.setItem(`${cacheKey}_timestamp`, Date.now().toString())

        if (isMounted) {
          setHasFavorites(hasAny)
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Error checking favorites status:", error)

        // Try to use cache even if it's expired
        const cacheKey = `${FAVORITES_STATUS_CACHE_KEY}${user.uid}`
        const cachedStatus = localStorage.getItem(cacheKey)

        if (cachedStatus && isMounted) {
          setHasFavorites(cachedStatus === "true")
        } else if (isMounted) {
          setHasFavorites(false)
        }

        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    checkFavorites()

    // Subscribe to real-time updates
    const unsubscribe = favoritesEventEmitter.subscribe((newHasFavorites) => {
      if (isMounted) {
        setHasFavorites(newHasFavorites)
      }
    })

    return () => {
      isMounted = false
      unsubscribe()
    }
  }, [user])

  return { hasFavorites, isLoading }
}
